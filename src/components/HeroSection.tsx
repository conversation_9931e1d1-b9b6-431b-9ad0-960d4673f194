'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import gradientGL from 'gradient-gl';
import Button from '@/components/ui/Button';

const HeroSection = () => {
  const t = useTranslations('hero');

  useEffect(() => {
    // Initialize gradient-gl with the specified seed and target the gradient container
    // gradientGL('f2.fc78', '#hero-gradient-bg');
    // gradientGL('b2.9d38', '#hero-gradient-bg');
    // gradientGL('b5.ad0d', '#hero-gradient-bg');
    gradientGL('a2.aa8e', '#hero-gradient-bg');
  }, []);

  return (
    <section className="relative h-screen py-20 flex items-center justify-center overflow-hidden">
      {/* WebGL Gradient Background */}
      <div id="hero-gradient-bg" className="absolute inset-0 z-0"></div>
      <div className="bg-albatros-indigo-dye absolute left-0 top-0 w-full h-full mix-blend-multiply"></div>

      <div className="relative w-full mt-10 max-w-7xl mx-auto px-6 lg:px-8 z-20">
        <div className="space-y-8">
          {/* Main Title - Bold and Impactful */}
          <div className="space-y-8">
            <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold leading-[0.9] tracking-tight">
              <span className="block max-w-4xl text-albatros-ivory mb-4">
                {t('title')}
              </span>
            </h1>

            <div className="max-w-3xl">
              <p className="text-xl md:text-2xl text-albatros-ivory leading-relaxed font-light">
                {t('description')}
              </p>
            </div>
          </div>

          {/* Enhanced CTA with modern styling */}
          <div className="flex flex-col sm:flex-row gap-6">
            <Button variant="primary" size="md">
              {t('contactButton')}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
