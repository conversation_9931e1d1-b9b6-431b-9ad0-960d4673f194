[{"/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/layout.tsx": "1", "/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/page.tsx": "2", "/home/<USER>/Projects/AlbatrosDoc/src/app/layout.tsx": "3", "/home/<USER>/Projects/AlbatrosDoc/src/components/FeaturesSection.tsx": "4", "/home/<USER>/Projects/AlbatrosDoc/src/components/Footer.tsx": "5", "/home/<USER>/Projects/AlbatrosDoc/src/components/HeroSection.tsx": "6", "/home/<USER>/Projects/AlbatrosDoc/src/components/Navigation.tsx": "7", "/home/<USER>/Projects/AlbatrosDoc/src/i18n/request.ts": "8", "/home/<USER>/Projects/AlbatrosDoc/src/middleware.ts": "9", "/home/<USER>/Projects/AlbatrosDoc/src/components/AboutSection.tsx": "10", "/home/<USER>/Projects/AlbatrosDoc/src/components/CTASection.tsx": "11", "/home/<USER>/Projects/AlbatrosDoc/src/types/gradient-gl.d.ts": "12", "/home/<USER>/Projects/AlbatrosDoc/src/components/examples/ButtonExamples.tsx": "13", "/home/<USER>/Projects/AlbatrosDoc/src/components/ui/Button.tsx": "14", "/home/<USER>/Projects/AlbatrosDoc/src/components/ui/NavButton.tsx": "15", "/home/<USER>/Projects/AlbatrosDoc/src/lib/utils.ts": "16"}, {"size": 2664, "mtime": 1755121266978, "results": "17", "hashOfConfig": "18"}, {"size": 571, "mtime": 1755086495650, "results": "19", "hashOfConfig": "18"}, {"size": 298, "mtime": 1755084901171, "results": "20", "hashOfConfig": "18"}, {"size": 5707, "mtime": 1755116542424, "results": "21", "hashOfConfig": "18"}, {"size": 12669, "mtime": 1755121334688, "results": "22", "hashOfConfig": "18"}, {"size": 1890, "mtime": 1755120508771, "results": "23", "hashOfConfig": "18"}, {"size": 8578, "mtime": 1755120683245, "results": "24", "hashOfConfig": "18"}, {"size": 532, "mtime": 1755085757690, "results": "25", "hashOfConfig": "18"}, {"size": 687, "mtime": 1755094467644, "results": "26", "hashOfConfig": "18"}, {"size": 6968, "mtime": 1755086826664, "results": "27", "hashOfConfig": "18"}, {"size": 7620, "mtime": 1755116992580, "results": "28", "hashOfConfig": "18"}, {"size": 263, "mtime": 1755121342616, "results": "29", "hashOfConfig": "18"}, {"size": 9219, "mtime": 1755117205775, "results": "30", "hashOfConfig": "18"}, {"size": 3453, "mtime": 1755120933533, "results": "31", "hashOfConfig": "18"}, {"size": 1766, "mtime": 1755116560791, "results": "32", "hashOfConfig": "18"}, {"size": 169, "mtime": 1755116375908, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gypqbj", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/layout.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/page.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/app/layout.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/FeaturesSection.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/Footer.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/HeroSection.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/Navigation.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/i18n/request.ts", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/middleware.ts", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/AboutSection.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/CTASection.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/types/gradient-gl.d.ts", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/examples/ButtonExamples.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/ui/Button.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/ui/NavButton.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/lib/utils.ts", [], []]