{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(bs|de|en)/:path*{(\\\\.json)}?", "originalSource": "/(bs|de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "dfe6397530ce91303d05a9b38c353e99", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f1aa57e266a61aa7fbee87fc3783eb302ff29185d946ac230239e06a1286f245", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d2663fe40c3bc906d6011f4e6b248f61f5fd7afac380cc7cd0cdb0cf3416369b"}}}, "instrumentation": null, "functions": {}}